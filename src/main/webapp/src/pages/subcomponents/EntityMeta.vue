<template>
  <div v-if="entity?.id" class="q-pa-md">
    <entity-meta-dates :entity="entity" class="q-mb-md" />

    <q-card v-if="hasRoleWriter" class="q-mb-md" flat bordered>
      <q-card-section>
        <q-input v-model="text" borderless :label="$t('afaktoApp.comment.text_helper')" rows="2" type="textarea">
          <template #append>
            <q-btn color="primary" icon="o_send" type="submit" @click="comment" />
          </template>
        </q-input>
      </q-card-section>
    </q-card>

    <q-timeline v-if="comments.length > 0" color="primary">
      <q-timeline-entry
        v-for="comment in comments"
        :key="comment.id"
        :title="comment.createdBy"
        :subtitle="$d(comment.createdDate, 'long')"
        icon="chat"
      >
        <div style="white-space: pre-line">{{ comment.text }}</div>
      </q-timeline-entry>
    </q-timeline>
  </div>
</template>

<script setup>
import pluralize from 'pluralize';

import { api } from 'boot/axios';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import EntityMetaDates from 'pages/subcomponents/EntityMetaDates.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';

const route = useRoute();

const baseApiUrl = '/api/comments';
const comments = ref([]);
// Try to get entity type from route, but handle special cases
const getEntityType = () => {
  const pathSegment = route.path.split('/')[1];
  const singular = pluralize.singular(pathSegment);

  // Based on CommentService.java, it uses entity.getClass().getSimpleName().toLowerCase()
  // So for Datastream entity, it should be "datastream" (lowercase)
  return singular.toLowerCase();
};

const entityType = getEntityType();
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const text = ref('');

const props = defineProps({
  entity: {
    type: Object,
    required: true,
  },
  entityType: {
    type: String,
    required: false,
  },
});

const loadComments = async () => {
  try {
    if (!props.entity?.id) {
      comments.value = [];
      return;
    }

    console.log('Loading comments for:', {
      entityId: props.entity.id,
      entityType: entityType,
      routePath: route.path
    });

    const response = await api.get(baseApiUrl, {
      params: {
        'relatedEntityId.equals': props.entity.id,
        'relatedEntityType.equals': entityType,
        sort: 'createdDate,desc'
      },
    });

    console.log('Comments response:', response.data);
    comments.value = response.data || [];
  } catch (error) {
    console.error('Failed to load comments:', error);
    comments.value = [];
  }
};

const comment = async () => {
  if (!text.value?.trim()) return;

  try {
    console.log('Adding comment:', {
      text: text.value.trim(),
      relatedEntityId: props.entity.id,
      relatedEntityType: entityType,
    });

    const newComment = await api.post(baseApiUrl, {
      text: text.value.trim(),
      relatedEntityId: props.entity.id,
      relatedEntityType: entityType,
    });

    console.log('Comment added:', newComment.data);

    // Add the new comment to the beginning of the list
    comments.value.unshift(newComment.data);
    text.value = '';
  } catch (error) {
    console.error('Failed to add comment:', error);
    // Optionally show user notification here
  }
};

watch(
  () => props.entity?.id,
  async (newId, oldId) => {
    if (newId && newId !== oldId) {
      await loadComments();
    } else if (!newId) {
      comments.value = []; // Clear comments when no id
    }
  },
  { immediate: true },
);
</script>
